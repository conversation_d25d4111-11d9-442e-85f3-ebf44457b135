/* LSTBook - Main Stylesheet */
/* Inspired by ABCya.com with child-friendly design */

/* ===== CSS VARIABLES ===== */
:root {
  /* Color Palette */
  --sky-blue: #4EC5F1;
  --sun-yellow: #FFCC00;
  --playful-red: #FF6B6B;
  --soft-green: #A5D6A7;
  --white: #FFFFFF;
  --light-gray: #F8F9FA;
  --dark-gray: #333333;
  --text-color: #2C3E50;
  
  /* Fonts */
  --primary-font: 'Comic Neue', cursive;
  --secondary-font: 'Nunito', sans-serif;
  
  /* Spacing */
  --container-max-width: 1200px;
  --section-padding: 60px 0;
  --border-radius: 15px;
  --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  
  /* Transitions */
  --transition: all 0.3s ease;
}

/* ===== RESET & BASE STYLES ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--primary-font);
  font-size: 16px;
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--white);
  overflow-x: hidden;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--primary-font);
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-color);
}

h1 {
  font-size: 2.5rem;
  color: var(--sky-blue);
}

h2 {
  font-size: 2rem;
  color: var(--playful-red);
}

h3 {
  font-size: 1.5rem;
}

h4 {
  font-size: 1.25rem;
}

p {
  margin-bottom: 1rem;
  font-family: var(--secondary-font);
}

a {
  color: var(--sky-blue);
  text-decoration: none;
  transition: var(--transition);
}

a:hover {
  color: var(--playful-red);
}

/* ===== LAYOUT ===== */
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 20px;
}

section {
  padding: var(--section-padding);
}

/* ===== NAVIGATION ===== */
#main-nav {
  background: #007969;
  box-shadow: var(--box-shadow);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.nav-container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.nav-logo a {
  display: flex;
  align-items: center;
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--white);
  text-decoration: none;
}

.nav-logo img {
  height: 50px;
  margin-right: 10px;
}

.logo-text {
  color: var(--white);
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 30px;
}

.nav-link {
  color: var(--white);
  font-weight: 600;
  font-size: 1.1rem;
  padding: 10px 15px;
  border-radius: var(--border-radius);
  transition: var(--transition);
  position: relative;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.app-link {
  background-color: var(--sun-yellow);
  color: var(--text-color) !important;
  font-weight: 700;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* Dropdown Menu */
.nav-dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: var(--white);
  min-width: 200px;
  box-shadow: var(--box-shadow);
  border-radius: var(--border-radius);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: var(--transition);
  z-index: 1001;
}

.nav-dropdown:hover .dropdown-menu,
.nav-dropdown.active .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-menu a {
  display: block;
  padding: 12px 20px;
  color: var(--text-color);
  border-bottom: 1px solid var(--light-gray);
  transition: var(--transition);
}

.dropdown-menu a:hover {
  background-color: var(--light-gray);
  color: var(--sky-blue);
}

.dropdown-menu a:last-child {
  border-bottom: none;
}

/* Mobile Navigation */
.nav-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
}

.nav-toggle span {
  width: 25px;
  height: 3px;
  background-color: var(--white);
  transition: var(--transition);
}

.nav-toggle.active span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.nav-toggle.active span:nth-child(2) {
  opacity: 0;
}

.nav-toggle.active span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* ===== BUTTONS ===== */
.cta-button, .play-button, .category-link, .submit-btn {
  display: inline-block;
  padding: 15px 30px;
  font-family: var(--primary-font);
  font-size: 1.1rem;
  font-weight: 700;
  text-align: center;
  text-decoration: none;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.cta-button {
  background: linear-gradient(135deg, var(--sun-yellow), var(--playful-red));
  color: var(--white);
}

.play-button {
  background: linear-gradient(135deg, var(--soft-green), var(--sky-blue));
  color: var(--white);
}

.category-link {
  background-color: var(--sky-blue);
  color: var(--white);
}

.submit-btn {
  background: linear-gradient(135deg, var(--playful-red), var(--sun-yellow));
  color: var(--white);
}

.cta-button:hover, .play-button:hover, .category-link:hover, .submit-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

/* ===== HERO SECTION ===== */
.hero {
  background: linear-gradient(135deg, #2c5aa0, #1e3a8a);
  color: var(--white);
  padding: 60px 0;
  position: relative;
  overflow: hidden;
  min-height: 500px;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="30" r="3" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="70" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="90" cy="80" r="2" fill="rgba(255,255,255,0.1)"/></svg>');
  animation: float 20s infinite linear;
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
  100% { transform: translateY(0px); }
}

.hero-layout {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 2;
  gap: 60px;
}

.hero-left {
  flex: 1;
  max-width: 500px;
}

.hero-title {
  font-size: 2.5rem;
  font-family: var(--primary-font);
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: 1.2rem;
  margin-bottom: 30px;
  opacity: 0.9;
}

/* Grade Cards */
.grade-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 30px;
}

.grade-card {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.grade-card:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.grade-card i {
  font-size: 24px;
  margin-bottom: 8px;
  display: block;
}

.grade-card span {
  font-family: var(--primary-font);
  font-weight: 600;
  font-size: 14px;
}

/* Grade Card Colors */
.all-games-card { border-color: #FFA726; }
.all-games-card i { color: #FFA726; }

.preschool-card { border-color: #EC407A; }
.preschool-card i { color: #EC407A; }

.elementary-card { border-color: #66BB6A; }
.elementary-card i { color: #66BB6A; }

.middle-card { border-color: #42A5F5; }
.middle-card i { color: #42A5F5; }

/* Arabic Banner */
.arabic-banner {
  background: linear-gradient(135deg, #00BCD4, #00838F);
  border-radius: 20px;
  padding: 15px 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.arabic-banner:hover {
  transform: scale(1.05);
}

.arabic-banner i {
  font-size: 20px;
  color: white;
}

.arabic-text {
  font-size: 18px;
  font-weight: bold;
  color: white;
  direction: rtl;
}

.english-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  margin-left: auto;
}

/* Hero Right - Device Frame */
.hero-right {
  flex: 0 0 350px;
}

/* iPad/TV Device Frame */
.device-frame {
  position: relative;
  background: linear-gradient(145deg, #2c2c2c, #1a1a1a);
  border-radius: 25px;
  padding: 20px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.3),
    inset 0 2px 4px rgba(255, 255, 255, 0.1);
}

.device-screen {
  background: #000;
  border-radius: 15px;
  padding: 3px;
  position: relative;
  overflow: hidden;
}

.device-screen::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1), transparent);
  border-radius: 15px;
  pointer-events: none;
}

.ad-content {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 12px;
  padding: 20px;
  color: #333;
  text-align: center;
  position: relative;
}

.ad-content h3 {
  font-family: var(--primary-font);
  font-size: 18px;
  margin-bottom: 15px;
  color: #2c5aa0;
}

.featured-games-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  margin-bottom: 15px;
}

.game-thumb {
  aspect-ratio: 1;
  background: linear-gradient(135deg, #4EC5F1, #42A5F5);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  cursor: pointer;
  transition: transform 0.2s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.game-thumb:hover {
  transform: scale(1.1);
}

.ad-tagline {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
  font-weight: 500;
}

.ad-badge {
  background: #007969;
  color: white;
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 10px;
  font-weight: 600;
  display: inline-block;
}

.device-base {
  height: 15px;
  background: linear-gradient(145deg, #3c3c3c, #2a2a2a);
  border-radius: 0 0 20px 20px;
  margin: 0 30px;
  position: relative;
}

.device-base::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 8px;
  background: linear-gradient(145deg, #4c4c4c, #3a3a3a);
  border-radius: 4px;
}

/* Grade Circles */
.grade-circles {
  display: flex;
  flex-direction: column;
  gap: 20px;
  flex: 1;
}

.grade-row {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.grade-circle {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 4px solid rgba(255, 255, 255, 0.3);
  position: relative;
}

.grade-circle.large {
  width: 140px;
  height: 140px;
  margin-bottom: 20px;
}

.grade-circle:hover {
  transform: scale(1.1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.grade-text {
  font-family: var(--primary-font);
  font-weight: 900;
  font-size: 14px;
  text-align: center;
  line-height: 1.2;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.grade-circle.large .grade-text {
  font-size: 18px;
}

/* Grade Circle Colors */
.all-games { background: linear-gradient(135deg, #FFA726, #FF8F00); }
.pre-k { background: linear-gradient(135deg, #EC407A, #C2185B); }
.grade-k { background: linear-gradient(135deg, #FFA726, #FF8F00); }
.grade-1 { background: linear-gradient(135deg, #EF5350, #C62828); }
.grade-2 { background: linear-gradient(135deg, #42A5F5, #1565C0); }
.grade-3 { background: linear-gradient(135deg, #66BB6A, #2E7D32); }
.grade-4 { background: linear-gradient(135deg, #AB47BC, #6A1B9A); }
.grade-5 { background: linear-gradient(135deg, #FF7043, #D84315); }
.grade-6 { background: linear-gradient(135deg, #26C6DA, #00838F); }

/* Language Circle */
.language-circle {
  position: absolute;
  left: 50px;
  bottom: 100px;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(135deg, #00BCD4, #00838F);
  border: 4px solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.language-circle:hover {
  transform: scale(1.1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.arabic-text {
  font-family: 'Arial', sans-serif;
  font-weight: 900;
  font-size: 16px;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  text-align: center;
  direction: rtl;
}

/* Printables Circle */
.printables-circle {
  position: absolute;
  right: 50px;
  bottom: 100px;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(135deg, #FFFFFF, #F5F5F5);
  border: 4px solid rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.printables-circle:hover {
  transform: scale(1.1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.printables-text {
  font-family: var(--primary-font);
  font-weight: 900;
  font-size: 14px;
  color: #333;
  text-align: center;
  line-height: 1.2;
}

/* Hero AdSense Block */
.hero-adsense {
  flex: 0 0 300px;
  margin-left: 40px;
}

.adsense-block {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  color: #333;
}

.ad-header {
  font-family: var(--primary-font);
  font-weight: 700;
  font-size: 16px;
  color: #333;
  margin-bottom: 15px;
  text-align: center;
}

.ad-games-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
  margin-bottom: 15px;
}

.ad-game-tile {
  aspect-ratio: 1;
  background: linear-gradient(135deg, #4EC5F1, #42A5F5);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.ad-game-tile:hover {
  transform: scale(1.05);
}

.ad-footer {
  text-align: center;
}

.ad-text {
  font-size: 12px;
  color: #666;
  line-height: 1.3;
  display: block;
  margin-bottom: 8px;
}

.ad-label {
  font-size: 10px;
  color: #999;
  background: #f0f0f0;
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-block;
}

/* ===== ADSENSE PLACEHOLDERS ===== */
.adsense-container {
  display: flex;
  justify-content: center;
  margin: 30px 0;
  padding: 20px;
}

.adsense-placeholder {
  background: linear-gradient(135deg, var(--light-gray), #e9ecef);
  border: 2px dashed #dee2e6;
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: var(--secondary-font);
  color: #6c757d;
  font-weight: 600;
  text-align: center;
}

#adsense-placeholder-banner {
  width: 728px;
  height: 90px;
  max-width: 100%;
}

#adsense-placeholder-sidebar {
  width: 300px;
  height: 250px;
}

.adsense-placeholder.vertical {
  width: 300px;
  height: 600px;
}

.adsense-container.bottom {
  margin-top: 40px;
}

/* ===== GAME CATEGORIES ===== */
.categories {
  background-color: var(--light-gray);
}

.categories h2 {
  text-align: center;
  margin-bottom: 50px;
  font-size: 2.5rem;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 40px;
}

.category-card {
  background: var(--white);
  border-radius: var(--border-radius);
  padding: 30px;
  text-align: center;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  border: 3px solid transparent;
  position: relative;
  overflow: hidden;
}

.category-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  transition: var(--transition);
  opacity: 0;
}

.category-card:hover::before {
  opacity: 1;
  animation: shine 0.6s ease-in-out;
}

@keyframes shine {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.category-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.category-icon {
  font-size: 3rem;
  margin-bottom: 20px;
}

.category-card h3 {
  margin-bottom: 15px;
  color: var(--text-color);
}

.category-card p {
  margin-bottom: 25px;
  color: #666;
}

/* ===== ANIMATIONS ===== */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .nav-menu {
    position: fixed;
    top: 70px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 70px);
    background: linear-gradient(135deg, var(--sky-blue), var(--soft-green));
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding-top: 50px;
    transition: var(--transition);
  }
  
  .nav-menu.active {
    left: 0;
  }
  
  .nav-toggle {
    display: flex;
  }
  
  .hero h1 {
    font-size: 2rem;
  }
  
  .categories-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  #adsense-placeholder-banner {
    width: 320px;
    height: 50px;
  }
  
  .container {
    padding: 0 15px;
  }
}

@media (max-width: 480px) {
  .hero {
    padding: 60px 0;
  }

  .hero h1 {
    font-size: 1.8rem;
  }

  .category-card {
    padding: 20px;
  }

  .cta-button, .play-button, .category-link {
    padding: 12px 20px;
    font-size: 1rem;
  }
}

/* ===== FEATURED GAME SECTION ===== */
.featured-game {
  background: var(--white);
}

.featured-game h2 {
  text-align: center;
  margin-bottom: 40px;
}

.game-showcase {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  align-items: center;
}

.game-preview img {
  width: 100%;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.game-info h3 {
  color: var(--sky-blue);
  margin-bottom: 15px;
}

.game-info p {
  margin-bottom: 25px;
  font-size: 1.1rem;
}

/* ===== VIDEO PREVIEWS ===== */
.video-previews {
  background: var(--light-gray);
}

.video-previews h2 {
  text-align: center;
  margin-bottom: 40px;
}

.videos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.video-item {
  background: var(--white);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}

.video-item:hover {
  transform: translateY(-5px);
}

.video-item iframe {
  width: 100%;
  height: 200px;
  border: none;
}

.video-item h4 {
  padding: 15px 20px 5px;
  color: var(--sky-blue);
}

.video-item p {
  padding: 0 20px 20px;
  margin: 0;
  color: #666;
}

/* ===== NEWSLETTER SECTION ===== */
.newsletter {
  background: linear-gradient(135deg, var(--playful-red), var(--sun-yellow));
  color: var(--white);
  text-align: center;
}

.newsletter h2 {
  color: var(--white);
  margin-bottom: 15px;
}

.newsletter p {
  margin-bottom: 30px;
  font-size: 1.1rem;
}

.newsletter-form {
  display: flex;
  justify-content: center;
  gap: 15px;
  max-width: 500px;
  margin: 0 auto;
}

.newsletter-form input {
  flex: 1;
  padding: 15px 20px;
  border: none;
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-family: var(--secondary-font);
}

.newsletter-form button {
  padding: 15px 25px;
  background: var(--white);
  color: var(--playful-red);
  border: none;
  border-radius: var(--border-radius);
  font-weight: 700;
  cursor: pointer;
  transition: var(--transition);
}

.newsletter-form button:hover {
  background: var(--light-gray);
  transform: translateY(-2px);
}

/* ===== FOOTER ===== */
#main-footer {
  background: var(--dark-gray);
  color: var(--white);
  padding: 50px 0 20px;
}

.footer-container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 30px;
}

.footer-section h3 {
  color: var(--sky-blue);
  margin-bottom: 20px;
}

.footer-section h4 {
  color: var(--sun-yellow);
  margin-bottom: 15px;
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 8px;
}

.footer-section ul li a {
  color: #ccc;
  transition: var(--transition);
}

.footer-section ul li a:hover {
  color: var(--sky-blue);
}

.social-icons {
  display: flex;
  gap: 15px;
  margin-top: 20px;
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--sky-blue);
  color: var(--white);
  border-radius: 50%;
  transition: var(--transition);
}

.social-icon:hover {
  background: var(--playful-red);
  transform: translateY(-3px);
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #555;
  color: #ccc;
}

/* ===== GAMES HUB STYLES ===== */
.games-hub {
  padding: 40px 0;
}

.games-hub h1 {
  text-align: center;
  margin-bottom: 40px;
}

.filters {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.filter-group label {
  font-weight: 600;
  margin-bottom: 5px;
  color: var(--text-color);
}

.filter-group select {
  padding: 10px 15px;
  border: 2px solid var(--sky-blue);
  border-radius: var(--border-radius);
  font-family: var(--secondary-font);
  background: var(--white);
  cursor: pointer;
}

.content-with-sidebar {
  display: grid;
  grid-template-columns: 1fr 320px;
  gap: 40px;
}

.main-content {
  min-height: 500px;
}

.sidebar {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* ===== CATEGORY PAGE STYLES ===== */
.category-page {
  padding: 40px 0;
}

.category-header {
  text-align: center;
  margin-bottom: 50px;
}

.category-header .category-icon {
  font-size: 4rem;
  color: var(--sky-blue);
  margin-bottom: 20px;
}

.category-header h1 {
  margin-bottom: 15px;
}

.category-header p {
  font-size: 1.2rem;
  color: #666;
}

.games-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.game-card {
  background: var(--white);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}

.game-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.game-card img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.game-card h3 {
  padding: 15px 20px 5px;
  color: var(--sky-blue);
}

.game-card p {
  padding: 0 20px;
  color: #666;
  margin-bottom: 15px;
}

.game-meta {
  padding: 0 20px;
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.age-tag, .difficulty-tag {
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.age-tag {
  background: var(--soft-green);
  color: var(--white);
}

.difficulty-tag.easy {
  background: var(--soft-green);
  color: var(--white);
}

.difficulty-tag.medium {
  background: var(--sun-yellow);
  color: var(--text-color);
}

.difficulty-tag.hard {
  background: var(--playful-red);
  color: var(--white);
}

.game-card .play-button {
  margin: 0 20px 20px;
  display: block;
  text-align: center;
}

/* ===== GAME PAGE STYLES ===== */
.game-page {
  padding: 40px 0;
}

.game-header {
  text-align: center;
  margin-bottom: 40px;
}

.game-header h1 {
  margin-bottom: 15px;
}

.game-header p {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 20px;
}

.game-container {
  background: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  margin-bottom: 40px;
}

.game-frame {
  position: relative;
  width: 100%;
  height: 500px;
  background: var(--light-gray);
}

.game-frame iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.game-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #666;
}

.game-placeholder i {
  font-size: 4rem;
  color: var(--sky-blue);
  margin-bottom: 20px;
}

.loading-animation {
  margin-top: 20px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--light-gray);
  border-top: 4px solid var(--sky-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.game-controls {
  display: flex;
  justify-content: center;
  gap: 15px;
  padding: 20px;
  background: var(--light-gray);
}

.control-btn {
  padding: 10px 20px;
  background: var(--sky-blue);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-family: var(--secondary-font);
  font-weight: 600;
}

.control-btn:hover {
  background: var(--playful-red);
  transform: translateY(-2px);
}

.game-instructions {
  background: var(--white);
  padding: 30px;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  margin-bottom: 40px;
}

.game-instructions h3 {
  color: var(--sky-blue);
  margin-bottom: 20px;
}

.game-instructions ul {
  list-style: none;
  padding-left: 0;
}

.game-instructions li {
  padding: 8px 0;
  padding-left: 30px;
  position: relative;
}

.game-instructions li::before {
  content: '🎮';
  position: absolute;
  left: 0;
  top: 8px;
}

.related-games {
  background: var(--light-gray);
  padding: 30px;
  border-radius: var(--border-radius);
}

.related-games h3 {
  text-align: center;
  margin-bottom: 30px;
  color: var(--playful-red);
}

.related-games-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.mini-game-card {
  background: var(--white);
  border-radius: var(--border-radius);
  padding: 20px;
  text-align: center;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}

.mini-game-card:hover {
  transform: translateY(-3px);
}

.mini-game-card img {
  width: 100%;
  height: 120px;
  object-fit: cover;
  border-radius: var(--border-radius);
  margin-bottom: 15px;
}

.mini-game-card h4 {
  margin-bottom: 15px;
  color: var(--sky-blue);
}

.mini-game-card a {
  display: inline-block;
  padding: 8px 16px;
  background: var(--soft-green);
  color: var(--white);
  border-radius: var(--border-radius);
  text-decoration: none;
  font-weight: 600;
  transition: var(--transition);
}

.mini-game-card a:hover {
  background: var(--playful-red);
}

/* ===== LEARN PAGE STYLES ===== */
.learn-page {
  padding: 40px 0;
}

.learn-page h1 {
  text-align: center;
  margin-bottom: 20px;
}

.learn-page > .container > p {
  text-align: center;
  font-size: 1.2rem;
  margin-bottom: 50px;
  color: #666;
}

.subject-section {
  margin-bottom: 60px;
}

.subject-section h2 {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 30px;
  color: var(--sky-blue);
}

.subject-section h2 i {
  font-size: 2rem;
}

/* ===== ABOUT PAGE STYLES ===== */
.about-page {
  padding: 40px 0;
}

.about-hero {
  text-align: center;
  margin-bottom: 60px;
}

.mission-statement {
  font-size: 1.5rem;
  color: var(--sky-blue);
  font-weight: 600;
  margin-bottom: 0;
}

.about-content {
  max-width: 800px;
  margin: 0 auto;
}

.about-section {
  margin-bottom: 50px;
}

.about-section h2 {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 25px;
}

.about-section h2 i {
  color: var(--sky-blue);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  margin-top: 30px;
}

.feature-card {
  background: var(--white);
  padding: 30px;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  text-align: center;
  transition: var(--transition);
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-card i {
  font-size: 3rem;
  color: var(--sky-blue);
  margin-bottom: 20px;
}

.feature-card h3 {
  margin-bottom: 15px;
  color: var(--text-color);
}

.goals-list {
  list-style: none;
  padding-left: 0;
}

.goals-list li {
  padding: 10px 0;
  padding-left: 40px;
  position: relative;
}

.goals-list li::before {
  content: '✨';
  position: absolute;
  left: 0;
  top: 10px;
  font-size: 1.2rem;
}

/* ===== CONTACT PAGE STYLES ===== */
.contact-page {
  padding: 40px 0;
}

.contact-hero {
  text-align: center;
  margin-bottom: 60px;
}

.contact-hero p {
  font-size: 1.2rem;
  color: #666;
}

.contact-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 60px;
  max-width: 1000px;
  margin: 0 auto;
}

.contact-form-section h2 {
  margin-bottom: 30px;
  color: var(--sky-blue);
}

.contact-form {
  background: var(--white);
  padding: 40px;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.form-group {
  margin-bottom: 25px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: var(--text-color);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #ddd;
  border-radius: var(--border-radius);
  font-family: var(--secondary-font);
  font-size: 1rem;
  transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--sky-blue);
  box-shadow: 0 0 0 3px rgba(78, 197, 241, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.contact-info h2 {
  margin-bottom: 30px;
  color: var(--playful-red);
}

.contact-method {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 30px;
  padding: 20px;
  background: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.contact-method i {
  font-size: 1.5rem;
  color: var(--sky-blue);
  margin-top: 5px;
}

.contact-method h3 {
  margin-bottom: 5px;
  color: var(--text-color);
}

.contact-method p {
  margin: 0;
  color: #666;
}

.faq-link {
  background: var(--light-gray);
  padding: 25px;
  border-radius: var(--border-radius);
  text-align: center;
}

.faq-btn {
  display: inline-block;
  padding: 10px 20px;
  background: var(--sky-blue);
  color: var(--white);
  border-radius: var(--border-radius);
  text-decoration: none;
  font-weight: 600;
  transition: var(--transition);
  margin-top: 15px;
}

.faq-btn:hover {
  background: var(--playful-red);
  transform: translateY(-2px);
}

/* ===== LEGAL PAGES STYLES ===== */
.legal-page {
  padding: 40px 0;
}

.legal-page h1 {
  text-align: center;
  margin-bottom: 20px;
}

.last-updated {
  text-align: center;
  color: #666;
  font-style: italic;
  margin-bottom: 50px;
}

.legal-content {
  max-width: 800px;
  margin: 0 auto;
  background: var(--white);
  padding: 40px;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.legal-section {
  margin-bottom: 40px;
}

.legal-section h2 {
  color: var(--sky-blue);
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid var(--light-gray);
}

.legal-section h3 {
  color: var(--playful-red);
  margin-top: 25px;
  margin-bottom: 15px;
}

.legal-section ul {
  padding-left: 25px;
}

.legal-section li {
  margin-bottom: 8px;
  line-height: 1.6;
}

/* ===== APP PAGE STYLES ===== */
.app-page {
  padding: 40px 0;
}

.app-hero {
  text-align: center;
  margin-bottom: 60px;
}

.app-icon {
  font-size: 5rem;
  color: var(--sky-blue);
  margin-bottom: 30px;
}

.app-hero h1 {
  margin-bottom: 20px;
}

.app-hero p {
  font-size: 1.3rem;
  color: #666;
}

.app-content {
  max-width: 1000px;
  margin: 0 auto;
}

.app-features {
  margin-bottom: 60px;
}

.app-features h2 {
  text-align: center;
  margin-bottom: 40px;
  color: var(--playful-red);
}

.feature-item {
  background: var(--white);
  padding: 30px;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  text-align: center;
  transition: var(--transition);
}

.feature-item:hover {
  transform: translateY(-5px);
}

.feature-item i {
  font-size: 3rem;
  color: var(--sky-blue);
  margin-bottom: 20px;
}

.feature-item h3 {
  margin-bottom: 15px;
  color: var(--text-color);
}

.app-preview {
  text-align: center;
  margin-bottom: 60px;
}

.app-preview h2 {
  margin-bottom: 40px;
  color: var(--sky-blue);
}

.phone-mockup {
  display: inline-block;
  background: #333;
  padding: 20px;
  border-radius: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.phone-screen {
  width: 300px;
  height: 500px;
  background: var(--white);
  border-radius: 20px;
  overflow: hidden;
  position: relative;
}

.app-screenshot {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.mock-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 2px solid var(--light-gray);
}

.mock-logo {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--sky-blue);
}

.mock-profile {
  font-size: 2rem;
}

.mock-games {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.mock-game {
  padding: 15px;
  background: var(--light-gray);
  border-radius: var(--border-radius);
  font-weight: 600;
  color: var(--text-color);
}

.mock-progress {
  margin-top: 20px;
  text-align: center;
}

.progress-bar {
  width: 100%;
  height: 10px;
  background: var(--light-gray);
  border-radius: 5px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-fill {
  width: 60%;
  height: 100%;
  background: linear-gradient(90deg, var(--sky-blue), var(--soft-green));
}

.notify-section {
  background: var(--light-gray);
  padding: 50px;
  border-radius: var(--border-radius);
  text-align: center;
  margin-bottom: 60px;
}

.notify-section h2 {
  margin-bottom: 20px;
  color: var(--sky-blue);
}

.notify-section p {
  margin-bottom: 30px;
  font-size: 1.1rem;
}

.notify-form .form-group {
  display: flex;
  justify-content: center;
  gap: 15px;
  max-width: 500px;
  margin: 0 auto;
}

.notify-form input {
  flex: 1;
  padding: 15px 20px;
  border: 2px solid var(--sky-blue);
  border-radius: var(--border-radius);
  font-size: 1rem;
}

.notify-form button {
  padding: 15px 25px;
  background: var(--sky-blue);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  font-weight: 700;
  cursor: pointer;
  transition: var(--transition);
}

.notify-form button:hover {
  background: var(--playful-red);
  transform: translateY(-2px);
}

.form-note {
  font-size: 0.9rem;
  color: #666;
  margin-top: 10px;
}

.platforms {
  text-align: center;
  margin-bottom: 60px;
}

.platforms h2 {
  margin-bottom: 30px;
  color: var(--playful-red);
}

.platform-icons {
  display: flex;
  justify-content: center;
  gap: 40px;
  flex-wrap: wrap;
}

.platform {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.platform i {
  font-size: 3rem;
  color: var(--sky-blue);
}

.platform span {
  font-weight: 600;
  color: var(--text-color);
}

.timeline {
  background: var(--white);
  padding: 40px;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.timeline h2 {
  text-align: center;
  margin-bottom: 40px;
  color: var(--sky-blue);
}

.timeline-items {
  position: relative;
  max-width: 600px;
  margin: 0 auto;
}

.timeline-items::before {
  content: '';
  position: absolute;
  left: 20px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--light-gray);
}

.timeline-item {
  position: relative;
  margin-bottom: 40px;
  padding-left: 60px;
}

.timeline-marker {
  position: absolute;
  left: 10px;
  top: 5px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--light-gray);
  border: 3px solid var(--white);
  box-shadow: 0 0 0 3px var(--light-gray);
}

.timeline-item.completed .timeline-marker {
  background: var(--soft-green);
  box-shadow: 0 0 0 3px var(--soft-green);
}

.timeline-item.current .timeline-marker {
  background: var(--sun-yellow);
  box-shadow: 0 0 0 3px var(--sun-yellow);
  animation: pulse 2s infinite;
}

.timeline-content h3 {
  margin-bottom: 10px;
  color: var(--text-color);
}

.timeline-content p {
  color: #666;
  margin: 0;
}

/* ===== RESPONSIVE UPDATES ===== */
@media (max-width: 768px) {
  .game-showcase {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .newsletter-form {
    flex-direction: column;
    align-items: center;
  }

  .newsletter-form input {
    margin-bottom: 15px;
  }

  .content-with-sidebar {
    grid-template-columns: 1fr;
  }

  .sidebar {
    order: -1;
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .notify-form .form-group {
    flex-direction: column;
  }

  .platform-icons {
    gap: 20px;
  }

  .timeline-items::before {
    left: 15px;
  }

  .timeline-item {
    padding-left: 50px;
  }

  .timeline-marker {
    left: 5px;
  }
}

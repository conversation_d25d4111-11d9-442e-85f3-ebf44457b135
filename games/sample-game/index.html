<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Math Adventure Quest - Fun educational game on LSTBook">
    <title>Math Adventure Quest - LSTBook</title>
    <link rel="stylesheet" href="../../css/style.css">
    <link rel="icon" type="image/png" href="../../assets/favicon.png">
    <link href="https://fonts.googleapis.com/css2?family=Comic+Neue:wght@300;400;700&family=Nunito:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <nav id="main-nav"></nav>
    
    <section class="game-page">
        <div class="container">
            <div class="game-header">
                <h1>Math Adventure Quest</h1>
                <p>Join our friendly characters on an exciting math journey!</p>
                <div class="game-meta">
                    <span class="age-tag">Ages 6-10</span>
                    <span class="difficulty-tag medium">Medium</span>
                    <span class="category-tag">Math</span>
                </div>
            </div>

            <!-- Game Area -->
            <div class="game-container">
                <div class="game-frame">
                    <iframe id="game-iframe" src="about:blank" title="Math Adventure Quest Game">
                        <!-- Fallback content -->
                        <div class="game-placeholder">
                            <i class="fas fa-gamepad"></i>
                            <h3>Game Loading...</h3>
                            <p>Get ready for an amazing math adventure!</p>
                            <div class="loading-animation">
                                <div class="spinner"></div>
                            </div>
                        </div>
                    </iframe>
                </div>
                
                <!-- Game Controls -->
                <div class="game-controls">
                    <button id="fullscreen-btn" class="control-btn">
                        <i class="fas fa-expand"></i> Fullscreen
                    </button>
                    <button id="restart-btn" class="control-btn">
                        <i class="fas fa-redo"></i> Restart
                    </button>
                    <button id="help-btn" class="control-btn">
                        <i class="fas fa-question"></i> Help
                    </button>
                </div>
            </div>

            <!-- Game Instructions -->
            <div class="game-instructions">
                <h3>How to Play</h3>
                <ul>
                    <li>Use your mouse or touch to interact with the game</li>
                    <li>Solve math problems to help the character advance</li>
                    <li>Collect stars and unlock new levels</li>
                    <li>Have fun while learning!</li>
                </ul>
            </div>

            <!-- AdSense Placeholder - Bottom Banner -->
            <div class="adsense-container bottom">
                <div id="adsense-placeholder-game-bottom" class="adsense-placeholder">
                    <!-- Google AdSense Banner Ad (728x90) - COPPA Compliant -->
                    <p>Advertisement Space</p>
                </div>
            </div>

            <!-- Related Games -->
            <div class="related-games">
                <h3>More Games You Might Like</h3>
                <div class="related-games-grid">
                    <div class="mini-game-card">
                        <img src="../../assets/game-placeholder.png" alt="Number Ninja">
                        <h4>Number Ninja</h4>
                        <a href="../number-ninjas.html">Play</a>
                    </div>
                    <div class="mini-game-card">
                        <img src="../../assets/game-placeholder.png" alt="Brain Booster">
                        <h4>Brain Booster</h4>
                        <a href="../brain-boosters.html">Play</a>
                    </div>
                    <div class="mini-game-card">
                        <img src="../../assets/game-placeholder.png" alt="Shape Shifter">
                        <h4>Shape Shifter</h4>
                        <a href="../shape-shifters.html">Play</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <footer id="main-footer"></footer>
    <script src="../../js/scripts.js"></script>
    <script>
        // Simple game simulation
        document.addEventListener('DOMContentLoaded', function() {
            const iframe = document.getElementById('game-iframe');
            setTimeout(() => {
                iframe.srcdoc = `
                    <html>
                    <head>
                        <style>
                            body { margin: 0; padding: 20px; font-family: 'Comic Neue', cursive; background: linear-gradient(45deg, #4EC5F1, #A5D6A7); text-align: center; color: white; }
                            .game-content { padding: 50px; }
                            .score { font-size: 24px; margin-bottom: 20px; }
                            .question { font-size: 32px; margin: 30px 0; }
                            .answers { display: flex; justify-content: center; gap: 20px; }
                            .answer-btn { padding: 15px 25px; font-size: 20px; border: none; border-radius: 15px; background: #FFCC00; color: #333; cursor: pointer; }
                            .answer-btn:hover { background: #FF6B6B; color: white; }
                        </style>
                    </head>
                    <body>
                        <div class="game-content">
                            <div class="score">Score: 0</div>
                            <div class="question">What is 5 + 3?</div>
                            <div class="answers">
                                <button class="answer-btn" onclick="alert('Correct! Great job!')">8</button>
                                <button class="answer-btn" onclick="alert('Try again!')">7</button>
                                <button class="answer-btn" onclick="alert('Try again!')">9</button>
                            </div>
                        </div>
                    </body>
                    </html>
                `;
            }, 1000);
        });
    </script>
</body>
</html>

// LSTBook - Main JavaScript File
// This file contains all the interactive functionality for the website

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the website
    initNavigation();
    initGameCategories();
    initForms();
    initAnimations();
});

// Navigation functionality
function initNavigation() {
    // Create and insert navigation
    const nav = document.getElementById('main-nav');
    if (nav) {
        nav.innerHTML = `
            <div class="nav-container">
                <div class="nav-logo">
                    <a href="/index.html">
                        <img src="/assets/logo.png" alt="LSTBook" onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                        <span class="logo-text">LSTBook</span>
                    </a>
                </div>
                <div class="nav-menu" id="nav-menu">
                    <a href="/index.html" class="nav-link">Home</a>
                    <div class="nav-dropdown">
                        <a href="/games/index.html" class="nav-link dropdown-toggle">Games <i class="fas fa-chevron-down"></i></a>
                        <div class="dropdown-menu">
                            <a href="/games/brain-boosters.html">Brain Boosters</a>
                            <a href="/games/number-ninjas.html">Number Ninjas</a>
                            <a href="/games/alphabet-explorers.html">Alphabet Explorers</a>
                            <a href="/games/puzzle-planet.html">Puzzle Planet</a>
                            <a href="/games/shape-shifters.html">Shape Shifters</a>
                            <a href="/games/color-creativity.html">Color & Creativity</a>
                            <a href="/games/sound-music.html">Sound & Music</a>
                            <a href="/games/world-wonders.html">World Wonders</a>
                            <a href="/games/daily-fun.html">Daily Fun</a>
                            <a href="/games/create-code.html">Create & Code</a>
                            <a href="/games/parent-picks.html">Parent Picks</a>
                        </div>
                    </div>
                    <a href="/learn.html" class="nav-link">Learn</a>
                    <a href="/about.html" class="nav-link">About</a>
                    <a href="/contact.html" class="nav-link">Contact</a>
                    <a href="/privacy.html" class="nav-link">Privacy</a>
                    <a href="/terms.html" class="nav-link">Terms</a>
                    <a href="/app.html" class="nav-link app-link">Coming Soon (App)</a>
                </div>
                <div class="nav-toggle" id="nav-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        `;
    }

    // Mobile menu toggle
    const navToggle = document.getElementById('nav-toggle');
    const navMenu = document.getElementById('nav-menu');
    
    if (navToggle && navMenu) {
        navToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            navToggle.classList.toggle('active');
        });
    }

    // Dropdown functionality
    const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
    dropdownToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            const dropdown = this.parentElement;
            dropdown.classList.toggle('active');
        });
    });

    // Create and insert footer
    const footer = document.getElementById('main-footer');
    if (footer) {
        footer.innerHTML = `
            <div class="footer-container">
                <div class="footer-content">
                    <div class="footer-section">
                        <h3>LSTBook</h3>
                        <p>Making learning fun and free for all kids!</p>
                        <div class="social-icons">
                            <a href="#" class="social-icon"><i class="fab fa-facebook-f"></i></a>
                            <a href="#" class="social-icon"><i class="fab fa-twitter"></i></a>
                            <a href="#" class="social-icon"><i class="fab fa-instagram"></i></a>
                            <a href="#" class="social-icon"><i class="fab fa-youtube"></i></a>
                        </div>
                    </div>
                    <div class="footer-section">
                        <h4>Games</h4>
                        <ul>
                            <li><a href="/games/brain-boosters.html">Brain Boosters</a></li>
                            <li><a href="/games/number-ninjas.html">Number Ninjas</a></li>
                            <li><a href="/games/alphabet-explorers.html">Alphabet Explorers</a></li>
                            <li><a href="/games/puzzle-planet.html">Puzzle Planet</a></li>
                        </ul>
                    </div>
                    <div class="footer-section">
                        <h4>Learn</h4>
                        <ul>
                            <li><a href="/learn.html">Educational Videos</a></li>
                            <li><a href="/about.html">About Us</a></li>
                            <li><a href="/contact.html">Contact</a></li>
                            <li><a href="/app.html">Mobile App</a></li>
                        </ul>
                    </div>
                    <div class="footer-section">
                        <h4>Legal</h4>
                        <ul>
                            <li><a href="/privacy.html">Privacy Policy</a></li>
                            <li><a href="/terms.html">Terms of Use</a></li>
                            <li><a href="/contact.html">Support</a></li>
                        </ul>
                    </div>
                </div>
                <div class="footer-bottom">
                    <p>&copy; 2024 LSTBook. All rights reserved. | Making learning fun for kids worldwide!</p>
                </div>
            </div>
        `;
    }
}

// Game categories data and initialization
function initGameCategories() {
    const categories = [
        {
            name: 'Brain Boosters',
            icon: 'fas fa-brain',
            description: 'Logic and thinking games',
            link: '/games/brain-boosters.html',
            color: '#4EC5F1'
        },
        {
            name: 'Number Ninjas',
            icon: 'fas fa-calculator',
            description: 'Math and counting fun',
            link: '/games/number-ninjas.html',
            color: '#FFCC00'
        },
        {
            name: 'Alphabet Explorers',
            icon: 'fas fa-font',
            description: 'Letters and reading adventures',
            link: '/games/alphabet-explorers.html',
            color: '#FF6B6B'
        },
        {
            name: 'Puzzle Planet',
            icon: 'fas fa-puzzle-piece',
            description: 'Jigsaw and logic puzzles',
            link: '/games/puzzle-planet.html',
            color: '#A5D6A7'
        },
        {
            name: 'Shape Shifters',
            icon: 'fas fa-shapes',
            description: 'Geometry and patterns',
            link: '/games/shape-shifters.html',
            color: '#4EC5F1'
        },
        {
            name: 'Color & Creativity',
            icon: 'fas fa-palette',
            description: 'Art and creative expression',
            link: '/games/color-creativity.html',
            color: '#FFCC00'
        },
        {
            name: 'Sound & Music',
            icon: 'fas fa-music',
            description: 'Musical games and rhythm',
            link: '/games/sound-music.html',
            color: '#FF6B6B'
        },
        {
            name: 'World Wonders',
            icon: 'fas fa-globe',
            description: 'Geography and cultures',
            link: '/games/world-wonders.html',
            color: '#A5D6A7'
        },
        {
            name: 'Daily Fun Games',
            icon: 'fas fa-calendar-day',
            description: 'New games every day',
            link: '/games/daily-fun.html',
            color: '#4EC5F1'
        },
        {
            name: 'Create & Code',
            icon: 'fas fa-code',
            description: 'Programming for kids',
            link: '/games/create-code.html',
            color: '#FFCC00'
        },
        {
            name: 'Parent Picks',
            icon: 'fas fa-heart',
            description: 'Recommended by parents',
            link: '/games/parent-picks.html',
            color: '#FF6B6B'
        }
    ];

    // Populate categories grid on homepage
    const categoriesGrid = document.querySelector('.categories-grid');
    if (categoriesGrid) {
        categoriesGrid.innerHTML = categories.map(category => `
            <div class="category-card" style="border-color: ${category.color}">
                <div class="category-icon" style="color: ${category.color}">
                    <i class="${category.icon}"></i>
                </div>
                <h3>${category.name}</h3>
                <p>${category.description}</p>
                <a href="${category.link}" class="category-link" style="background-color: ${category.color}">
                    Play Now!
                </a>
            </div>
        `).join('');
    }

    // Populate games grid on games index page
    const gamesGrid = document.getElementById('games-grid');
    if (gamesGrid) {
        gamesGrid.innerHTML = categories.map(category => `
            <div class="category-card" style="border-color: ${category.color}">
                <div class="category-icon" style="color: ${category.color}">
                    <i class="${category.icon}"></i>
                </div>
                <h3>${category.name}</h3>
                <p>${category.description}</p>
                <a href="${category.link}" class="category-link" style="background-color: ${category.color}">
                    Explore Games
                </a>
            </div>
        `).join('');
    }
}

// Form handling
function initForms() {
    // Newsletter form
    const newsletterForm = document.querySelector('.newsletter-form');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const email = this.querySelector('input[type="email"]').value;
            alert('Thank you for subscribing! We\'ll keep you updated with the latest games and activities.');
            this.reset();
        });
    }

    // Contact form
    const contactForm = document.getElementById('contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Thank you for your message! We\'ll get back to you within 24-48 hours.');
            this.reset();
        });
    }

    // App notification form
    const appNotifyForm = document.getElementById('app-notify-form');
    if (appNotifyForm) {
        appNotifyForm.addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Great! We\'ll notify you as soon as our mobile app is ready!');
            this.reset();
        });
    }
}

// Animations and interactions
function initAnimations() {
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add loading animation to game cards
    const gameCards = document.querySelectorAll('.game-card, .category-card');
    gameCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in');
    });
}

// Filter functionality for games page
function initFilters() {
    const ageFilter = document.getElementById('age-filter');
    const difficultyFilter = document.getElementById('difficulty-filter');
    const typeFilter = document.getElementById('type-filter');

    if (ageFilter && difficultyFilter && typeFilter) {
        [ageFilter, difficultyFilter, typeFilter].forEach(filter => {
            filter.addEventListener('change', applyFilters);
        });
    }
}

function applyFilters() {
    // This would filter the games based on selected criteria
    // For now, it's a placeholder
    console.log('Filters applied');
}

// Initialize filters if on games page
if (window.location.pathname.includes('/games/')) {
    document.addEventListener('DOMContentLoaded', initFilters);
}

<svg width="280" height="70" viewBox="0 0 280 70" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradient definitions for bubble effect -->
    <linearGradient id="gradientL" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#A5D6A7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#66BB6A;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gradientS" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#FFCC00;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFA000;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gradientT" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#FF6B6B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E53935;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gradientB" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#4EC5F1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1976D2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gradientO" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#9C27B0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6A1B9A;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gradientO2" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#FF9800;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E65100;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gradientK" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#00BCD4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0097A7;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Letter L -->
  <g transform="translate(10, 10)">
    <path d="M5 5 Q15 0 25 5 Q30 10 25 20 L25 35 Q25 45 15 45 L10 45 Q0 45 0 35 L0 15 Q0 5 10 5 Z"
          fill="url(#gradientL)" stroke="white" stroke-width="3"/>
    <text x="12" y="32" font-family="Comic Neue, cursive" font-size="24" font-weight="900" fill="white" text-anchor="middle">L</text>
  </g>

  <!-- Letter S -->
  <g transform="translate(50, 10)">
    <path d="M5 5 Q15 0 25 5 Q30 10 25 20 Q30 25 25 35 Q25 45 15 45 L10 45 Q0 45 0 35 L0 15 Q0 5 10 5 Z"
          fill="url(#gradientS)" stroke="white" stroke-width="3"/>
    <text x="12" y="32" font-family="Comic Neue, cursive" font-size="24" font-weight="900" fill="white" text-anchor="middle">S</text>
  </g>

  <!-- Letter T -->
  <g transform="translate(90, 10)">
    <path d="M5 5 Q15 0 25 5 Q30 10 25 20 L25 35 Q25 45 15 45 L10 45 Q0 45 0 35 L0 15 Q0 5 10 5 Z"
          fill="url(#gradientT)" stroke="white" stroke-width="3"/>
    <text x="12" y="32" font-family="Comic Neue, cursive" font-size="24" font-weight="900" fill="white" text-anchor="middle">T</text>
  </g>

  <!-- Letter B -->
  <g transform="translate(130, 10)">
    <path d="M5 5 Q15 0 25 5 Q30 10 25 20 L25 35 Q25 45 15 45 L10 45 Q0 45 0 35 L0 15 Q0 5 10 5 Z"
          fill="url(#gradientB)" stroke="white" stroke-width="3"/>
    <text x="12" y="32" font-family="Comic Neue, cursive" font-size="24" font-weight="900" fill="white" text-anchor="middle">B</text>
  </g>

  <!-- Letter o -->
  <g transform="translate(170, 15)">
    <path d="M5 5 Q12 0 20 5 Q25 10 20 20 L20 25 Q20 35 12 35 L8 35 Q0 35 0 25 L0 15 Q0 5 8 5 Z"
          fill="url(#gradientO)" stroke="white" stroke-width="3"/>
    <text x="10" y="25" font-family="Comic Neue, cursive" font-size="18" font-weight="900" fill="white" text-anchor="middle">o</text>
  </g>

  <!-- Letter o -->
  <g transform="translate(205, 15)">
    <path d="M5 5 Q12 0 20 5 Q25 10 20 20 L20 25 Q20 35 12 35 L8 35 Q0 35 0 25 L0 15 Q0 5 8 5 Z"
          fill="url(#gradientO2)" stroke="white" stroke-width="3"/>
    <text x="10" y="25" font-family="Comic Neue, cursive" font-size="18" font-weight="900" fill="white" text-anchor="middle">o</text>
  </g>

  <!-- Letter k -->
  <g transform="translate(240, 15)">
    <path d="M5 5 Q12 0 20 5 Q25 10 20 20 L20 25 Q20 35 12 35 L8 35 Q0 35 0 25 L0 15 Q0 5 8 5 Z"
          fill="url(#gradientK)" stroke="white" stroke-width="3"/>
    <text x="10" y="25" font-family="Comic Neue, cursive" font-size="18" font-weight="900" fill="white" text-anchor="middle">k</text>
  </g>

  <!-- Small decorative stars -->
  <circle cx="35" cy="8" r="2" fill="#FFCC00" opacity="0.8"/>
  <circle cx="75" cy="6" r="1.5" fill="#FF6B6B" opacity="0.8"/>
  <circle cx="115" cy="7" r="2" fill="#A5D6A7" opacity="0.8"/>
  <circle cx="155" cy="8" r="1.5" fill="#4EC5F1" opacity="0.8"/>
</svg>
